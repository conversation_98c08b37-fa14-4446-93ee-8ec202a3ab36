#!/usr/bin/env python3
"""
重构验证测试脚本

用于验证spider_v2.py重构后的功能是否正常工作。
测试包括：
1. CookieManager类的导入和基本功能
2. 主要函数的导入
3. 类和函数的文档字符串检查

作者：AI Assistant
创建时间：2025-08-04
"""

import sys
import inspect
from typing import get_type_hints

def test_cookie_manager_import():
    """测试CookieManager类的导入"""
    try:
        from cookie_manager import CookieManager
        print("✓ CookieManager类导入成功")
        
        # 检查类的方法
        methods = [method for method in dir(CookieManager) if not method.startswith('_')]
        expected_methods = ['get_available_cookie', 'mark_cookie_invalid', 'switch_to_next_cookie']
        
        for method in expected_methods:
            if method in methods:
                print(f"✓ 方法 {method} 存在")
            else:
                print(f"✗ 方法 {method} 缺失")
                
        # 检查文档字符串
        if CookieManager.__doc__:
            print("✓ CookieManager类有文档字符串")
        else:
            print("✗ CookieManager类缺少文档字符串")
            
        return True
    except ImportError as e:
        print(f"✗ CookieManager导入失败: {e}")
        return False

def test_spider_functions():
    """测试spider_v2.py中主要函数的导入"""
    try:
        # 只导入不依赖外部服务的函数
        from spider_v2 import (
            get_random_user_agent,
            get_link_unique_key,
            format_registration_days,
            encode_image_to_base64
        )
        print("✓ 主要函数导入成功")
        
        # 测试get_random_user_agent
        user_agent = get_random_user_agent()
        if user_agent and isinstance(user_agent, str):
            print("✓ get_random_user_agent函数工作正常")
        else:
            print("✗ get_random_user_agent函数异常")
            
        # 测试get_link_unique_key
        test_link = "https://example.com/item?id=123&ref=search"
        unique_key = get_link_unique_key(test_link)
        expected = "https://example.com/item?id=123"
        if unique_key == expected:
            print("✓ get_link_unique_key函数工作正常")
        else:
            print(f"✗ get_link_unique_key函数异常: 期望 {expected}, 得到 {unique_key}")
            
        # 测试format_registration_days
        days_text = format_registration_days(365)
        if "年" in days_text:
            print("✓ format_registration_days函数工作正常")
        else:
            print("✗ format_registration_days函数异常")
            
        return True
    except ImportError as e:
        print(f"✗ 函数导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 函数测试失败: {e}")
        return False

def test_documentation():
    """检查重要函数的文档字符串"""
    try:
        from cookie_manager import CookieManager
        
        # 检查CookieManager方法的文档
        methods_to_check = ['get_available_cookie', 'mark_cookie_invalid', 'switch_to_next_cookie']
        
        for method_name in methods_to_check:
            method = getattr(CookieManager, method_name)
            if method.__doc__ and len(method.__doc__.strip()) > 10:
                print(f"✓ {method_name} 有详细文档")
            else:
                print(f"✗ {method_name} 缺少详细文档")
                
        return True
    except Exception as e:
        print(f"✗ 文档检查失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("开始重构验证测试...\n")
    
    tests = [
        ("CookieManager导入测试", test_cookie_manager_import),
        ("Spider函数测试", test_spider_functions),
        ("文档字符串测试", test_documentation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print(f"\n--- 测试总结 ---")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！重构成功！")
        return 0
    else:
        print("⚠️  部分测试失败，请检查重构结果")
        return 1

if __name__ == "__main__":
    sys.exit(main())
